/* GoodPage/src/app/dino/dino.module.css */

.container {
  position: fixed; /* Use fixed positioning to cover viewport */
  top: 0;
  left: 0;
  width: 100vw; /* Full viewport width */
  height: 100vh; /* Full viewport height */
  background-color: #f7f7f7;
  overflow: hidden; /* Hide internal overflow */
  font-family: sans-serif;
  color: #555;
  /* Remove border and padding from placeholder */
}

.ground {
  position: absolute;
  bottom: 30%;
  left: 0;
  width: 200%;
  height: 14px;
  background-image: repeating-linear-gradient(
    45deg,
    #d3d3d3 0,
    #d3d3d3 1px,
    transparent 1px,
    transparent 3px
  );
  background-size: 4px 4px;
  animation: scrollGround 5s linear infinite;
  z-index: 5;
}

@keyframes scrollGround {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-50%);
  }
}

.dino {
  position: absolute;
  bottom: 30%;
  left: 80px;
  font-size: 10rem;
  user-select: none;
  transform: scaleX(-1) translateY(15px); /* Final position adjustment */
  z-index: 10;
}

/* Keep jump animation definitions */
@keyframes jump {
  0% {
    transform: translateY(15px) scaleX(-1);
  }
  50% {
    /* Single peak frame */
    transform: translateY(calc(-200px + 15px)) scaleX(-1);
  }
  100% {
    transform: translateY(15px) scaleX(-1);
  }
}

.dino.jumping {
  animation-name: jump;
  animation-duration: 0.9s;
  animation-timing-function: cubic-bezier(0.3, 0.8, 0.7, 0.9);
}

/* Keep obstacle base style and movement animation */
.obstacle {
  position: absolute;
  bottom: calc(30% + 2px);
  user-select: none;
  z-index: 9;
  animation-name: moveObstacle;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
  right: -200px;
}

@keyframes moveObstacle {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(calc(-100vw - 150px));
  }
}

/* Keep obstacle specific styles (size, animation timing) */
.cactus1 {
  font-size: 4.8rem;
  bottom: calc(30% + 2px);
  animation-duration: 4s;
  animation-delay: 1s;
}

.cactus2 {
  font-size: 5.5rem;
  bottom: calc(30% + 2px);
  animation-duration: 4s;
  animation-delay: 3s;
}

.pterodactyl {
  font-size: 4.5rem;
  bottom: calc(30% + 250px);
  animation-duration: 5s;
  animation-delay: 2s;
}

/* Rename or reuse godModeText for the indicator */
.godModeIndicator /* or .godModeText */ {
  position: absolute;
  top: 20px;
  left: 20px;
  font-weight: normal; /* Remove bold if desired */
  color: #e74c3c; /* Red color for heart? */
  background-color: #f7f7f7; /* Match container background */
  padding: 8px 15px; /* Slightly larger padding */
  border-radius: 10px; /* Slightly larger radius */
  font-size: 2rem; /* Increase base font size */
  z-index: 20;
  display: flex; /* Use flex to align items */
  align-items: center;
  gap: 0.3em; /* Space between heart and infinity */
  user-select: none; /* Add this line to prevent selection */
}

/* Style for the infinity symbol */
.infinitySymbol {
  font-size: 2.2rem; /* Increase infinity symbol size */
  line-height: 1; /* Adjust line height for vertical alignment */
  color: #555; /* Different color? */
}

/* Keep instructions styles */
.instructions {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.8rem;
  color: #aaa;
  text-align: center;
  z-index: 20;
}

.instructions code {
  background-color: #ddd;
  padding: 1px 4px;
  border-radius: 3px;
  font-family: monospace;
}

/* Add flashing animation */
@keyframes flash {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  } /* Make it partially transparent */
}

/* Apply flashing animation when invulnerable */
.dino.flashing {
  animation: flash 0.15s step-end infinite; /* Fast flashing */
  /* Use step-end for distinct on/off states */
}

/* Animation for the god mode indicator when hit */
@keyframes indicatorHitAnimation {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(0.85); /* Slightly shrink */
    opacity: 0.7; /* Slightly fade */
  }
}

/* Apply the hit animation */
.godModeIndicator.indicatorHit {
  animation: indicatorHitAnimation 0.3s ease-in-out;
}

.canvasContainer {
  /* Make container fill the entire viewport */
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw; 
  height: 100vh; 
  
  /* Align canvas to the top */
  display: flex;
  justify-content: center;
  align-items: flex-start; /* Align canvas to the top */
  
  /* Remove padding used for partial screen offset */
  /* padding-top: 50px; */ 
  
  overflow: hidden; /* Hide potential scrollbars if canvas slightly overflows */
  background-color: #f7f7f7; /* Match page background or set desired game background */
  z-index: 100; /* Ensure it's above other page content if necessary */
}

/* Styles for the canvas itself */
.runnerCanvas {
  /* Dimensions will be set via JavaScript */
  background-color: #f7f7f7;
  /* Optional: Add a subtle border if needed */
  /* border: 1px solid #eee; */
}

/* Remove or comment out old, unused styles if they exist */
/* .game, .score, .dino, .jump, .cactus, .block, .container, .ground, etc. */
/* ... (ensure old conflicting styles are removed or commented out) ... */
