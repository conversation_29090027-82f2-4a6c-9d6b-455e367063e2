{"name": "homepage4good", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:setup": "node ./scripts/setup-db.mjs", "db:scan-gallery": "ts-node -T scripts/scan-gallery.ts", "db:export-data": "ts-node -T scripts/export-all-data.ts"}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.0.1", "@orcid/bibtex-parse-js": "^0.0.25", "@radix-ui/react-alert-dialog": "^1.1.10", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@types/bcrypt": "^5.0.2", "@types/generate-password": "^1.5.0", "@uiw/react-amap": "^7.1.4", "bcrypt": "^5.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "csv-parse": "^5.6.0", "fast-xml-parser": "^5.2.5", "framer-motion": "^12.7.3", "fs": "^0.0.1-security", "generate-password": "^1.7.1", "js-confetti": "^0.12.0", "lucide-react": "^0.492.0", "next": "15.3.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "react-masonry-css": "^1.0.16", "react-select": "^5.10.1", "sonner": "^2.0.3", "sqlite": "^5.1.1", "sqlite3": "^5.1.7", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5", "usehooks-ts": "^3.1.1", "uuid": "^11.1.0", "yarn": "^1.22.22", "zod": "^3.24.3", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@prisma/client": "6.6.0", "@tailwindcss/postcss": "^4", "@types/node": "^20.17.30", "@types/react": "^19.1.2", "@types/react-dom": "^19", "@types/react-select": "^5.0.0", "@types/sqlite3": "^3.1.11", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.29.1", "@typescript-eslint/parser": "^8.29.1", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.0", "globals": "^16.0.0", "postcss": "^8.5.3", "prisma": "6.6.0", "tailwind-scrollbar": "^4.0.2", "tailwindcss": "^4.1.3", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^8.29.1"}}