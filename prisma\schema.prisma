// prisma/schema.prisma

// 定义数据源 (datasource): 指定数据库连接方式
datasource db {
  provider = "sqlite" // Corrected: Set provider back to sqlite
  url      = env("DATABASE_URL") // Example for SQLite: "file:./dev.db"
}

// 定义 Prisma 客户端生成器 (generator)
generator client {
  provider = "prisma-client-js" // 指定生成 JavaScript/TypeScript 版本的 Prisma Client
  // output = "../node_modules/.prisma/client" // 可选: 明确指定客户端生成路径 (建议添加以消除警告)
}

// --- 枚举定义 (Enum Definitions) ---
// 使用枚举可以限制字段只能接受预定义的值，提高数据一致性和类型安全

// 成员状态枚举
enum MemberStatus {
  PROFESSOR        // 教师 (教授/副教授/讲师等)
  POSTDOC          // 博士后
  PHD_STUDENT      // 博士生
  MASTER_STUDENT   // 硕士生
  UNDERGRADUATE    // 本科生
  VISITING_SCHOLAR // 访问学者
  RESEARCH_STAFF   // 研究人员/工程师
  ALUMNI           // 校友 (已毕业或离开)
  OTHER            // 其他身份
}

// 出版物类型枚举
enum PublicationType {
  CONFERENCE       // 会议论文
  JOURNAL          // 期刊文章
  WORKSHOP         // Workshop 论文
  PREPRINT         // 预印本 (例如 arXiv 上的文章)
  BOOK_CHAPTER     // 书籍章节
  BOOK             // 整本书
  THESIS           // 学位论文 (博士/硕士)
  PATENT           // 专利 (虽然也定义了 Patent 模型，但有时也作为一种出版类型)
  TECHNICAL_REPORT // 技术报告
  OTHER            // 其他类型
}

// 软件/数据集类型枚举 (Artefact: 人工制品，泛指产出物)
enum ArtefactType {
  SOFTWARE // 软件项目/库
  DATASET  // 数据集
}

// 奖项等级枚举
enum AwardLevel {
  GOLD
  SILVER
  BRONZE
  OTHER // Default or unspecified
}

// --- 新增: 项目类型枚举 (用于主页项目展示) ---
enum ProjectType {
  MAIN   // 主要项目
  FORMER // 过往项目
}

// --- 数据模型定义 (Data Model Definitions) ---

// 成员模型 (代表实验室的每一个人)
model Member {
  // --- 核心身份信息 ---
  id               String  @id @default(cuid())
  name_en          String // Added _en suffix for clarity
  name_zh          String?
  status           MemberStatus      // 成员当前的状态 (使用 MemberStatus 枚举，必填)
  enrollment_year  Int?              // 入学或入职的年份 (可选，对于校友可能是毕业年份)
  graduation_year  Int?              // 毕业年份 (可选)

  // --- 职位与学术信息 ---
  title_zh         String?
  title_en         String? // Added _en suffix
  major            String?           // 专业领域 (例如 "Computer Science", "Software Engineering") (可选)
  research_group   String?           // 所属的内部科研小组或主要研究方向 (例如 "AI Systems Group", "Database Group") (可选，简单文本)
  research_interests String?
  skills           String?           // 掌握的技能 (同上，可以是逗号分隔文本) (可选)

  // --- 详细与个性化信息 ---
  bio_zh           String?
  bio_en           String?
  research_statement_zh String? // 新增: 长篇研究陈述 (中文)
  research_statement_en String? // 新增: 长篇研究陈述 (英文)
  more_about_me    String?           // "更多关于我"部分，可以放更个性化、非正式的介绍 (支持 Markdown 格式?) (可选)
  interests_hobbies String?          // 非学术方面的兴趣爱好 (可选)
  avatar_url       String?
  office_location  String? // Added field
  office_hours     String?           // 新增: 办公时间 (例如 "周一 10:00-12:00 或邮件预约") (可选)
  pronouns         String?           // 个人代词 (例如 "she/her", "he/him", "they/them") (可选)
  position         String? // General position if needed
  phone_number     String? // Added field
  personal_website String?
  github_username  String?
  linkedin_url     String?
  google_scholar_id String?
  dblp_url         String? // Added DBLP URL field
  orcid_id        String? // Added ORCID ID field
  cv_url           String?
  display_order    Int     @default(0)
  is_active        Boolean @default(true)
  role_name        String? // For RBAC
  username         String? @unique // For RBAC login
  password_hash    String? 

  // --- 联系与链接 ---
  email            String? @unique
  favorite_emojis   String?           // 网站的 favicon 图标的 URL (可选)

  // --- 状态与时间信息 ---
  start_date       DateTime?         // 加入实验室或开始当前身份的日期 (可选)
  graduation_details String?         // 毕业去向或其他离职后的信息 (主要用于校友) (可选)
  recruiting_status String?          // 招生/招聘/合作状态的文本描述 (主要用于教师/PI) (可选)
  is_profile_public Boolean  @default(true) // 个人主页信息默认是否公开可见
  createdAt        DateTime @default(now()) // 记录创建时间 (自动设置)
  updatedAt        DateTime @updatedAt      // 记录最后更新时间 (自动更新)

  // --- 关系定义 ---
  // 导师/学生关系 (Self-Relation, 自我关联)
  supervisor       Member?  @relation("Supervision", fields: [supervisor_id], references: [id], onDelete: SetNull, onUpdate: Cascade) // 指向其导师 (是可选的)
  supervisor_id    String?  @map("supervisor_id") // 存储导师 member.id 的外键列，允许为空。@map 用于指定数据库中的列名（可选，如果 Prisma 默认生成的符合预期）
  supervisees      Member[] @relation("Supervision") // 指向该成员指导的学生列表 (一对多关系的反向)

  // --- 关联到其他表 (表示该成员拥有的相关记录) ---
  authoredPublications PublicationAuthor[] @relation("MemberPublications") // 作为作者参与的出版物 (通过 PublicationAuthor 连接)
  educationHistory     Education[]         // 教育经历列表
  awards               Award[]             // 所获荣誉列表
  projects             ProjectMember[]     // 参与的项目列表 (通过 ProjectMember 连接)
  teachingRoles        Teaching[]          // 教学经历列表
  presentations        Presentation[]      // 学术报告列表
  softwareAndDatasets  SoftwareDataset[]   // 发布的软件/数据集列表
  patents              Patent[]            // 获得的专利列表
  academicServices     AcademicService[]   // 参与的学术服务列表
  newsMentions         News[]              // 提及该成员的新闻列表
  sponsorships         Sponsorship[]       // Add relation to Sponsorship

  // --- 新增: 反向关联到该成员负责的主页项目 ---
  ledHomepageProjects HomepageProject[] @relation("MemberLeadsHomepageProject")

  @@index([display_order])
  @@index([is_active])
  @@index([role_name])
}

// 教育经历模型 (Education Table)
model Education {
  id            Int      @id @default(autoincrement()) // 自增 ID 作为主键
  member        Member   @relation(fields: [member_id], references: [id], onDelete: Cascade) // 定义与 Member 的多对一关系，并设置级联删除
  member_id     String   // 外键，关联到 Member 表的 id
  degree        String   // 学位名称 (例如 "B.Eng.", "M.Sc.", "Ph.D.") (必填)
  field         String?  // 专业领域 (例如 "Computer Science") (可选)
  school        String   // 学校名称 (必填)
  start_year    Int?     // 开始年份 (可选)
  end_year      Int?     // 结束/毕业年份 (对于在读学生可以为空)
  thesis_title  String?  // 学位论文题目 (可选)
  description   String?  // 额外描述信息 (例如 "优等毕业", "交换生经历") (可选)
  display_order Int      @default(0) // 用于控制在列表中显示的顺序，数字越小越靠前

  @@index([member_id]) // 在 member_id 上创建索引，加速根据成员查询教育经历
}

// 奖项荣誉模型 (Award Table)
model Award {
  id            Int      @id @default(autoincrement()) // 自增 ID 主键
  member_id     String   // Relation to Member
  content       String   // Store the full description text
  year          Int?     // Attempt to extract the last year for sorting/filtering
  level         AwardLevel  @default(OTHER) // Visual level indicator
  link_url      String?  // Optional link
  display_order Int      @default(0) // 显示顺序
  isFeatured    Boolean  @default(false) // Manually controlled highlight

  // Relation
  member        Member   @relation(fields: [member_id], references: [id], onDelete: Cascade)

  @@index([member_id]) // 成员 ID 索引
  @@index([year])
  @@index([display_order])
}

// 项目模型 (Project Table)
model Project {
  id             Int      @id @default(autoincrement()) // 自增 ID 主键
  title          String   // 项目名称 (必填)
  description    String?  // 项目详细描述 (长文本) (可选)
  status         String?  // 项目当前状态 (例如 "进行中", "已完成", "核心项目") (可选)
  start_year     Int?     // 项目开始年份 (可选)
  end_year       Int?     // 项目结束年份 (可选)
  url            String?  // 项目主页、代码库或演示链接 (可选)
  funding_source String?  // 项目资金来源 (例如 "NSFC", "企业合作") (可选)
  logo_url       String?  // 项目 Logo 图片 URL (可选)
  tags           String?  // 项目标签或关键词 (逗号分隔文本?) (可选)
  is_featured    Boolean  @default(false) // 是否是特色项目 (可用于在首页等处突出显示)
  createdAt      DateTime @default(now()) // 记录创建时间
  updatedAt      DateTime @updatedAt      // 记录最后更新时间

  // 关系字段
  members        ProjectMember[] // 参与该项目的成员列表 (通过 ProjectMember 连接)
  // publications   ProjectPublication[] // 未来可以添加项目与出版物的关联
}

// 项目-成员关系模型 (ProjectMember Junction Table)
// 用于连接 Project 和 Member，实现多对多关系
model ProjectMember {
  project        Project @relation(fields: [project_id], references: [id], onDelete: Cascade) // 关联到 Project
  project_id     Int     // 外键
  member         Member  @relation(fields: [member_id], references: [id], onDelete: Cascade) // 关联到 Member
  member_id      String  // 外键
  role           String? // 成员在该项目中的角色 (例如 "负责人(PI)", "参与者", "主要开发者") (可选)

  @@id([project_id, member_id]) // 使用 project_id 和 member_id 组成复合主键
  @@index([member_id])         // 在 member_id 上加索引，方便查询某人参与的所有项目
}

// 教学经历模型 (Teaching Table)
model Teaching {
  id              Int      @id @default(autoincrement()) // 自增 ID 主键
  member          Member   @relation(fields: [member_id], references: [id], onDelete: Cascade) // 关联到 Member
  member_id       String   // 外键
  course_code     String?  // 课程代码 (可选)
  course_title    String   // 课程标题 (必填)
  semester        String?  // 开课学期 (例如 "Fall 2024", "Spring 2023") (可选)
  year            Int?     // 开课年份 (可选)
  role            String   @default("Instructor") // 担任角色 (默认为 "Instructor", 可以是 "TA", "Guest Lecturer" 等)
  university      String?  // 授课大学 (如果不是成员所在的主要大学) (可选)
  description_url String?  // 课程描述、大纲或资源的链接 (可选)
  display_order   Int      @default(0) // 显示顺序

  @@index([member_id]) // 成员 ID 索引
}

// 学术报告模型 (Presentation Table)
model Presentation {
  id            Int      @id @default(autoincrement()) // 自增 ID 主键
  member        Member   @relation(fields: [member_id], references: [id], onDelete: Cascade) // 关联到 Member
  member_id     String   // 外键
  title         String   // 报告标题 (必填)
  event_name    String?  // 作报告的会议或活动名称 (例如 "ICSE 2024", "Invited Talk at XYZ University") (可选)
  conference_url String? // 会议/活动链接 (可选)
  location      String?  // 报告地点 (例如 "Nanchang, China", "Virtual") (可选)
  year          Int?     // 报告年份 (可选)
  url           String?  // 幻灯片 (Slides) 或报告视频的链接 (可选)
  is_invited    Boolean  @default(false) // 是否是受邀报告 (Invited Talk)
  display_order Int      @default(0) // 显示顺序

  @@index([member_id]) // 成员 ID 索引
}

// 软件/数据集模型 (SoftwareDataset Table)
model SoftwareDataset {
  id             Int          @id @default(autoincrement()) // 自增 ID 主键
  member         Member       @relation(fields: [member_id], references: [id], onDelete: Cascade) // 关联到 Member (贡献者/维护者)
  member_id      String       // 外键
  title          String       // 软件或数据集的名称 (必填)
  description    String?      // 详细描述 (可选)
  type           ArtefactType @default(SOFTWARE) // 类型: 软件还是数据集 (使用 ArtefactType 枚举)
  repository_url String?      // 代码仓库链接 (例如 GitHub, GitLab) (可选)
  project_url    String?      // 项目主页、文档或演示链接 (可选)
  license        String?      // 使用的开源许可证 (例如 "MIT", "Apache 2.0") (可选)
  version        String?      // 当前版本号 (可选)
  status         String?      // 项目状态 (例如 "Active", "Beta", "Archived") (可选)
  display_order  Int          @default(0) // 显示顺序

  @@index([member_id]) // 成员 ID 索引
}

// 专利模型 (Patent Table)
model Patent {
  id             Int      @id @default(autoincrement()) // 自增 ID 主键
  member         Member   @relation(fields: [member_id], references: [id], onDelete: Cascade) // 关联到 Member (发明人之一)
  member_id      String   // 外键 (注意：专利通常有多个发明人，这里简化为只关联一个主要成员，或者需要多对多关系)
                          // 如果需要表示所有发明人，可能需要一个 PatentInventor 连接表或在 inventors_string 中存储
  title          String   // 专利名称 (必填)
  patent_number  String?  @unique // 专利号 (如果提供，应唯一)
  inventors_string String? // 发明人列表 (按专利官方格式的字符串) (可选)
  issue_date     String?  // 授权日期 (使用 String 保持格式灵活性, e.g., "YYYY-MM-DD") (可选)
  status         String?  // 专利状态 (例如 "申请中/Filed", "已授权/Granted") (可选)
  url            String?  // 专利查询链接 (例如 Google Patents URL) (可选)
  display_order  Int      @default(0) // 显示顺序

  @@index([member_id]) // 成员 ID 索引
}

// 学术服务模型 (AcademicService Table)
model AcademicService {
  id              Int      @id @default(autoincrement()) // 自增 ID 主键
  member_id       String   // Relation to Member
  organization    String?  // Added: Name of the organization (e.g., Conference Name, Journal Name) - Made optional
  role            String?  // Added: Role held (e.g., "PC Member", "Reviewer", "Session Chair") - Made optional
  start_year      Int?     // Added: Start year of the service (optional)
  end_year        Int?     // Added: End year of the service (optional)
  description     String?  // Added: Optional further description
  display_order   Int      @default(0)
  isFeatured      Boolean  @default(false) // Manually controlled highlight

  // Relation
  member          Member   @relation(fields: [member_id], references: [id], onDelete: Cascade)

  @@index([member_id]) // 成员 ID 索引
  @@index([display_order])
}

// 新闻/动态模型 (News Table)
model News {
  id                   Int      @id @default(autoincrement()) // 自增 ID 主键
  title                String   // 新闻标题 (必填)
  content              String   // 新闻内容 (长文本, 可能支持 Markdown) (必填)
  createdAt            DateTime @default(now()) // 新闻创建时间 (自动设置)
  updatedAt            DateTime @updatedAt      // 新闻最后更新时间 (自动更新)
  is_pinned            Boolean  @default(false) // 是否置顶显示

  // (可选) 将新闻关联到特定的出版物或成员
  related_publication Publication? @relation(fields: [related_publication_id], references: [id], onDelete: SetNull) // 关联到 Publication (可选, 设为 NULL 如果论文被删除)
  related_publication_id Int?      @unique // 强制一条新闻最多关联一篇论文
  related_member      Member?      @relation(fields: [related_member_id], references: [id], onDelete: SetNull) // 关联到 Member (可选, 设为 NULL 如果成员被删除)
  related_member_id   String?      // 外键
}


// 出版物模型 (Publication Table) - 增强版
model Publication {
  id                    Int              @id @default(autoincrement()) // 自增 ID 主键
  title                 String           // 论文标题 (必填)
  venue                 String?          // 发表地点 (会议/期刊名称) (可选)
  year                  Int              // 发表年份 (必填)
  volume                String?          // 卷号 (可选)
  number                String?          // 期号 (可选)
  pages                 String?          // 页码 (可选)
  publisher             String?          // 出版社 (可选)
  ccf_rank              String?          // CCF 等级 (可选)
  dblp_url              String?          // DBLP 链接 (可选)
  pdf_url               String?          // PDF 文件链接 (可选)
  abstract              String?          // 摘要 (可选)
  keywords              String?          // 关键词 (逗号分隔文本?) (可选)
  type                  PublicationType? @default(CONFERENCE) // 出版物类型 (使用枚举, 默认为会议)
  slides_url            String?          // 相关的幻灯片链接 (可选)
  video_url             String?          // 相关的报告视频链接 (可选)
  code_repository_url   String?          // 相关的代码库链接 (可选)
  project_page_url      String?          // 相关的项目主页链接 (可选)
  is_peer_reviewed      Boolean?         // 是否经过同行评审 (可选)
  publication_status    String?          // 发表状态 (例如 "Published", "Accepted", "Submitted", "Preprint") (可选)
  authors_full_string   String?          // 完整的、用于直接显示的作者列表字符串（包含格式和机构等）(可选)

  // --- Pending 相关字段 ---
  status                String?          @default("published") // 审核状态 ("pending_review", "published", "rejected")
  raw_authors           String?          // 原始作者字符串 (用于 pending 审核)
  source                String?          // 数据来源 ("manual", "bibtex_import", "yaml_import")

  createdAt             DateTime         @default(now()) // 记录创建时间
  updatedAt             DateTime         @updatedAt      // 记录最后更新时间

  // 关系字段
  authors               PublicationAuthor[] @relation("PublicationAuthors") // 作者列表 (通过 PublicationAuthor 连接)
  newsItems             News[]              // 与此出版物相关的新闻列表 (一对多)
}

// 出版物-作者关系模型 (PublicationAuthor Junction Table) - 增强版
model PublicationAuthor {
  // 外键和关系定义
  publication           Publication @relation("PublicationAuthors", fields: [publication_id], references: [id], onDelete: Cascade) // 关联到 Publication
  publication_id        Int         // 外键
  author                Member      @relation("MemberPublications", fields: [member_id], references: [id], onDelete: Cascade) // 关联到 Member
  member_id             String      // 外键

  // 关系属性
  author_order          Int         // 作者顺序 (必填)
  is_corresponding_author Boolean @default(false) // 是否为通讯作者 (可选)

  // --- 新增字段 ---
  isFeaturedOnProfile   Boolean     @default(false) // 是否在成员主页推荐
  profileDisplayOrder   Int         @default(0)     // 在成员主页推荐时的显示顺序

  // 约束和索引
  @@id([publication_id, member_id])     // 复合主键: 同一篇论文，一个作者只出现一次
  @@unique([publication_id, author_order]) // 唯一约束: 同一篇论文，一个顺序只有一个作者
  @@index([member_id])                  // 索引: 加速按作者查找论文
  @@index([member_id, isFeaturedOnProfile, profileDisplayOrder]) // 新增索引: 加速查询成员的推荐论文
}

// 照片标签模型 (用于管理所有标签)
model PhotoTag {
  id     Int      @id @default(autoincrement()) // 标签的唯一自增 ID
  name   String   @unique // 标签名称 (例如 "团建", "会议", "实验室日常", "2024级"), 必须唯一

  // 关系字段: 指向使用此标签的所有照片记录 (通过 LabPhotoTag 连接)
  photos LabPhotoTag[]
}

// 实验室照片模型 - 【修改】移除了上传者关联
model LabPhoto {
  id             Int      @id @default(autoincrement()) // 照片记录的唯一自增 ID
  url            String   // 照片文件的 URL 或存储路径 (必填)
  description    String?  // 照片的描述或说明文字 (可选)
  alt_text       String?  // 图像的替代文本 (用于 SEO 和无障碍访问) (可选)
  location       String?  // 拍摄地点 (可选)
  taken_at       DateTime? // 照片实际拍摄日期和时间 (可选)
  is_featured    Boolean  @default(false) // 是否为特色照片 (例如用于首页展示)
  display_order  Int      @default(0)     // 自定义显示顺序 (可选)
  createdAt      DateTime @default(now()) // 照片记录创建时间 (自动)
  updatedAt      DateTime @updatedAt      // 照片记录更新时间 (自动)

  // --- 移除了以下字段 ---
  // uploader    Member?  @relation(fields: [uploader_id], references: [id], onDelete: SetNull)
  // uploader_id String?
  // --- 移除了 uploader_id 索引 ---
  // @@index([uploader_id])

  // 关系字段: 指向该照片拥有的所有标签记录 (通过 LabPhotoTag 连接)
  tags           LabPhotoTag[]

  // 为其他可能常用的查询字段添加索引
  @@index([taken_at])    // 如果经常按拍摄时间排序或查询
  @@index([is_featured]) // 如果经常查询特色照片
}

// 照片-标签关系模型 (连接表) - (保持不变)
model LabPhotoTag {
  photo    LabPhoto @relation(fields: [photo_id], references: [id], onDelete: Cascade)
  photo_id Int
  tag      PhotoTag @relation(fields: [tag_id], references: [id], onDelete: Cascade)
  tag_id   Int

  assignedAt DateTime @default(now())

  @@id([photo_id, tag_id])
  @@index([tag_id])
}

// 赞助模型 (Sponsorship Table)
model Sponsorship { // Includes Grants & Funding
  id            Int     @id @default(autoincrement())
  member_id     String  // Relation to Member
  content       String  // Store the full description text
  period        String? // Year or period string for isFeatured check
  link_url      String? // Optional link
  display_order Int     @default(0)
  isFeatured    Boolean @default(false) // Manually controlled highlight

  // Relation
  member Member @relation(fields: [member_id], references: [id], onDelete: Cascade)

  @@index([member_id])
  @@index([display_order])
}

// --- 新增: 主页板块元数据模型 ---
// 用于存储各可编辑板块的通用信息，如标题、介绍文本等
model HomepageSectionMeta {
  section_id   String    @id // 板块的唯一标识符 (例如 "news", "student_interests", "main_projects", "teaching")
  title        String    // 板块显示的标题 (例如 "News", "Students with Interests")
  introduction String?   // 板块的介绍性文本 (例如新闻板块的招聘信息) (长文本，可选)
  is_visible   Boolean   @default(true) // 控制该板块是否在主页显示
  updatedAt    DateTime  @updatedAt // 最后更新时间

  @@map("homepage_section_meta") // 指定数据库表名
}

// --- 新增: 主页新闻条目模型 ---
model HomepageNews {
  id            Int      @id @default(autoincrement()) // 自增 ID 主键
  content       String   // 新闻内容 (单条新闻文本)
  display_order Int      @default(0) // 显示顺序
  is_visible    Boolean  @default(true) // 是否显示该条新闻
  createdAt     DateTime @default(now()) // 创建时间
  updatedAt     DateTime @updatedAt // 更新时间

  @@index([display_order])
  @@map("homepage_news") // 指定数据库表名
}

// --- 新增: 学生兴趣点模型 ---
// 用于 "Students with Interests" 板块下的各个要点
model InterestPoint {
  id            Int      @id @default(autoincrement()) // 自增 ID 主键
  title         String   // 兴趣点的子标题
  description   String   // 兴趣点的描述文本 (长文本)
  display_order Int      @default(0) // 显示顺序
  is_visible    Boolean  @default(true) // 是否显示该兴趣点
  createdAt     DateTime @default(now()) // 创建时间
  updatedAt     DateTime @updatedAt // 更新时间

  @@index([display_order])
  @@map("interest_points") // 指定数据库表名
}

// --- 新增: 主页项目模型 ---
// 用于 "Main Focus Projects" 和 "Former Projects" 板块
model HomepageProject {
  id            Int         @id @default(autoincrement()) // 自增 ID 主键
  title         String      // 项目标题
  description   String      // 项目描述 (长文本)
  image_url     String?     // 项目图片 URL (可选)
  project_url   String?     // 项目相关链接 (例如代码库、演示页面) (可选)
  type          ProjectType @default(MAIN) // 项目类型 (主要项目 / 过往项目)
  display_order Int         @default(0) // 显示顺序
  is_visible    Boolean     @default(true) // 是否显示该项目
  createdAt     DateTime    @default(now()) // 创建时间
  updatedAt     DateTime    @updatedAt // 更新时间

  // --- 新增: 关联到项目负责人 ---
  leader        Member?     @relation("MemberLeadsHomepageProject", fields: [leader_id], references: [id], onDelete: SetNull, onUpdate: Cascade) // 指向负责人 (可选, Member 删除时设为 NULL)
  leader_id     String?     // 外键, 关联到 Member 表的 id

  @@index([type, display_order])
  @@index([leader_id]) // 为负责人 ID 添加索引
  @@map("homepage_projects") // 指定数据库表名
}

// --- 新增: 主页教学信息模型 ---
// 用于 "Teaching" 板块
model HomepageTeaching {
  id            Int      @id @default(autoincrement()) // 自增 ID 主键
  course_title  String   // 课程标题 (例如 "Introduction to Artificial Intelligence")
  details       String?  // 教学详情 (例如 "Fall 2024, Fall 2023, Fall 2022...") (可选)
  display_order Int      @default(0) // 显示顺序
  is_visible    Boolean  @default(true) // 是否显示该教学信息
  createdAt     DateTime @default(now()) // 创建时间
  updatedAt     DateTime @updatedAt // 更新时间

  @@index([display_order])
  @@map("homepage_teaching") // 指定数据库表名
}

model GalleryPhoto {
  id           Int      @id @default(autoincrement())
  filename     String   // The filename including path relative to /public/images/gallery
  category     String   // One of: Meetings, Graduation, Team Building, Sports, Lab Life, Competition
  caption      String?  // Optional caption for the photo
  date         String?  // Optional date for the photo
  is_visible   Boolean  @default(true) // Whether the photo should be displayed in its category
  show_in_albums Boolean @default(false) // Whether the photo should be displayed in the Albums view
  display_order Int     @default(0) // Order within its category
  albums_order Int     @default(0) // Order in the Albums view
  created_at   DateTime @default(now())
  updated_at   DateTime @updatedAt

  @@unique([filename])
  @@index([category, is_visible])
  @@index([show_in_albums])
}
