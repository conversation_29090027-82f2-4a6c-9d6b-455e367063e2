id,title,description,display_order,is_visible,createdAt,updatedAt
"1","Functional Programming and Implementation","Functional programming is a programming paradigm where programs are constructed by applying and composing functions. The paradigm has been widely used in the context of compiler, high-performance computing, etc. We aim to leverage functional programming to build efficient systems.","0",true,"2025-04-27T10:01:04.978Z","2025-04-27T14:29:34.004Z"
"2","Compile Systems and Context Sensitivity","Compiler technology is the foundation of modern programming languages and systems. We are particularly interested in the context-sensitive analysis of programs, which provides deeper insights into program behavior and potential optimizations.","1",true,"2025-04-27T10:01:33.209Z","2025-04-27T14:29:34.004Z"
"3","Distributed Systems and Consistency","In distributed systems, ensuring data consistency across multiple nodes is challenging but crucial. We're exploring novel approaches to maintain consistency without sacrificing performance, especially in dynamic network environments.","2",true,"2025-04-27T10:01:46.565Z","2025-04-27T14:29:34.004Z"