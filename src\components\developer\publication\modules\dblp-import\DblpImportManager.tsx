"use client";

import React, { useState, useRef, useEffect } from "react";
import { themeColors } from "@/styles/theme";
import {
  Loader2,
  Download,
  CheckCircle,
  ExternalLink,
  Plus,
  RefreshCw,
  Trash2,
  FileText
} from "lucide-react";
import { toast } from "sonner";

interface DblpFile {
  name: string;
  size: number;
  lastModified: string;
  path: string;
}

interface ImportResult {
  imported: number;
  duplicatesSkipped: number;
  total: number;
  duplicateTitles: string[];
  fileName: string;
}

/**
 * DBLP Import Manager Component
 * Provides functionality to upload DBLP output files and import publications
 */
const DblpImportManager: React.FC = () => {
  const [dblpFiles, setDblpFiles] = useState<DblpFile[]>([]);
  const [isLoadingFiles, setIsLoadingFiles] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 组件挂载时加载文件列表
  useEffect(() => {
    loadDblpFiles();
  }, []);

  /**
   * Load file list
   */
  const loadDblpFiles = async () => {
    setIsLoadingFiles(true);
    try {
      const response = await fetch('/api/publications/dblp-files');
      const result = await response.json();

      if (response.ok && result.success) {
        setDblpFiles(result.data);
      } else {
        throw new Error(result.error || 'Failed to load files');
      }
    } catch (error) {
      console.error('Load files error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to load files: ${errorMessage}`);
    } finally {
      setIsLoadingFiles(false);
    }
  };

  /**
   * 处理文件选择
   */
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
    // 清空 input 值，允许重复选择同一文件
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  /**
   * Upload file
   */
  const handleFileUpload = async (file: File) => {
    setIsUploading(true);
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/publications/dblp-files', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success(`File "${file.name}" uploaded successfully!`);
        await loadDblpFiles(); // Reload file list
      } else {
        throw new Error(result.error || 'Upload failed');
      }
    } catch (error) {
      console.error('Upload error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Upload failed: ${errorMessage}`);
    } finally {
      setIsUploading(false);
    }
  };

  /**
   * Delete file
   */
  const handleDeleteFile = async (fileName: string) => {
    if (!confirm(`Are you sure you want to delete file "${fileName}"?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/publications/dblp-files/${encodeURIComponent(fileName)}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success(`File "${fileName}" deleted successfully!`);
        await loadDblpFiles(); // Reload file list
      } else {
        throw new Error(result.error || 'Delete failed');
      }
    } catch (error) {
      console.error('Delete error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Delete failed: ${errorMessage}`);
    }
  };

  /**
   * Import DBLP file
   */
  const handleImportFile = async (fileName: string) => {
    if (!fileName) {
      toast.error('Please select a file to import');
      return;
    }

    setIsImporting(true);
    setImportResult(null);

    try {
      const response = await fetch('/api/publications/import-dblp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fileName: fileName,
        }),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        setImportResult(result.data);
        toast.success(`Successfully imported ${result.data.imported} publications!`);

        if (result.data.duplicatesSkipped > 0) {
          toast.warning(`Skipped ${result.data.duplicatesSkipped} duplicate publications`);
        }
      } else {
        throw new Error(result.error || 'Import failed');
      }
    } catch (error) {
      console.error('Import error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Import failed: ${errorMessage}`);
    } finally {
      setIsImporting(false);
    }
  };

  /**
   * Format file size
   */
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  /**
   * Format date
   */
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleString('en-US');
  };

  return (
    <div className="space-y-6">
      {/* 标题 */}
      <div className="flex items-center justify-between">
        <h2 className={`text-2xl font-bold ${themeColors.devText}`}>
          DBLP Import
        </h2>
        <div className="flex items-center gap-2 text-sm text-gray-400">
          <ExternalLink className="w-4 h-4" />
          <span>Import from DBLP output files</span>
        </div>
      </div>

      {/* 文件管理区域 */}
      <div className="space-y-6">
        {/* 上传按钮 */}
        <div className="flex justify-between items-center">
          <h3 className={`text-lg font-medium ${themeColors.devText}`}>DBLP Files Management</h3>
          <div className="flex gap-3">
            <input
              ref={fileInputRef}
              type="file"
              accept=".txt"
              onChange={handleFileSelect}
              className="hidden"
            />
            <button
              onClick={() => fileInputRef.current?.click()}
              disabled={isUploading}
              className={`inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${
                isUploading
                  ? "bg-gray-600 cursor-not-allowed"
                  : "bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              } transition-colors disabled:opacity-50`}
            >
              {isUploading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Uploading...
                </>
              ) : (
                <>
                  <Plus className="w-4 h-4 mr-2" />
                  Upload DBLP File
                </>
              )}
            </button>
            <button
              onClick={loadDblpFiles}
              disabled={isLoadingFiles}
              className={`inline-flex items-center px-4 py-2 border border-gray-600 rounded-md shadow-sm text-sm font-medium ${themeColors.devText} ${themeColors.devCardBg} hover:bg-gray-700 transition-colors disabled:opacity-50`}
            >
              {isLoadingFiles ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <RefreshCw className="w-4 h-4 mr-2" />
              )}
              Refresh
            </button>
          </div>
        </div>

        {/* 文件列表 */}
        <div className={`rounded-lg ${themeColors.devCardBg} border border-gray-600`}>
          {isLoadingFiles ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="w-6 h-6 animate-spin text-blue-400" />
              <span className={`ml-2 ${themeColors.devText}`}>加载文件列表...</span>
            </div>
          ) : dblpFiles.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="w-12 h-12 text-gray-400 mx-auto mb-3" />
              <p className={`${themeColors.devDescText} mb-2`}>
                No DBLP files available
              </p>
              <p className={`text-sm ${themeColors.devDescText}`}>
                Click "Upload DBLP File" button above to get started
              </p>
            </div>
          ) : (
            <div className="divide-y divide-gray-600">
              {dblpFiles.map((file, index) => (
                <div key={index} className="p-4 flex items-center justify-between hover:bg-gray-700/50 transition-colors">
                  <div className="flex items-center space-x-3">
                    <FileText className="w-5 h-5 text-blue-400" />
                    <div>
                      <p className={`font-medium ${themeColors.devText}`}>{file.name}</p>
                      <p className={`text-sm ${themeColors.devDescText}`}>
                        {formatFileSize(file.size)} • {formatDate(file.lastModified)}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleImportFile(file.name)}
                      disabled={isImporting}
                      className={`inline-flex items-center px-3 py-1 border border-transparent rounded text-sm font-medium text-white ${
                        isImporting
                          ? "bg-gray-600 cursor-not-allowed"
                          : "bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                      } transition-colors disabled:opacity-50`}
                    >
                      {isImporting ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                          Importing...
                        </>
                      ) : (
                        <>
                          <Download className="w-4 h-4 mr-1" />
                          Import
                        </>
                      )}
                    </button>
                    <button
                      onClick={() => handleDeleteFile(file.name)}
                      className="inline-flex items-center px-3 py-1 border border-red-600 rounded text-sm font-medium text-red-400 hover:bg-red-600 hover:text-white transition-colors"
                    >
                      <Trash2 className="w-4 h-4 mr-1" />
                      Delete
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Import Results */}
      {importResult && (
        <div className={`p-6 rounded-lg ${themeColors.devCardBg} border border-green-600`}>
          <div className="flex items-center gap-3 mb-4">
            <CheckCircle className="w-6 h-6 text-green-400" />
            <h3 className={`text-lg font-medium ${themeColors.devText}`}>Import Completed</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">{importResult.imported}</div>
              <div className={`text-sm ${themeColors.devDescText}`}>Successfully Imported</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-400">{importResult.duplicatesSkipped}</div>
              <div className={`text-sm ${themeColors.devDescText}`}>Duplicates Skipped</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-400">{importResult.total}</div>
              <div className={`text-sm ${themeColors.devDescText}`}>Total Processed</div>
            </div>
          </div>

          <div className="mb-4">
            <p className={`text-sm ${themeColors.devDescText}`}>
              文件: <span className="text-blue-400">{importResult.fileName}</span>
            </p>
          </div>

          {importResult.duplicateTitles.length > 0 && (
            <div className="mt-4">
              <p className={`text-sm ${themeColors.devDescText} mb-2`}>跳过的重复论文:</p>
              <div className="max-h-32 overflow-y-auto">
                {importResult.duplicateTitles.map((title, index) => (
                  <div key={index} className="text-sm text-yellow-400 mb-1">
                    • {title}
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="mt-4 p-3 bg-blue-900/50 border border-blue-600 rounded-md">
            <p className="text-blue-400 text-sm">
              💡 导入的论文已添加到 "Pending Review" 标签页，您可以在那里进行审核和编辑后发布。
            </p>
          </div>
        </div>
      )}

      {/* 使用说明 */}
      <div className={`p-4 rounded-md bg-gray-800/50 border border-gray-600`}>
        <h4 className={`font-medium ${themeColors.devText} mb-2`}>使用说明:</h4>
        <ul className={`text-sm ${themeColors.devDescText} space-y-1`}>
          <li>• <strong>上传:</strong> 点击"上传 DBLP 文件"添加 DBLP 爬虫的 .txt 输出文件</li>
          <li>• <strong>导入:</strong> 点击任意可用文件的"导入"按钮来导入论文到待审核状态</li>
          <li>• <strong>删除:</strong> 点击"删除"按钮从系统中移除文件</li>
          <li>• <strong>格式:</strong> 仅支持 .txt 格式的 DBLP 输出文件（最大 10MB）</li>
          <li>• <strong>去重:</strong> 系统会自动检测并跳过已存在的重复论文（基于标题匹配）</li>
          <li>• <strong>审核:</strong> 导入的论文会出现在 "Pending Review" 标签页等待审核</li>
          <li>• <strong>数据源:</strong> 使用 <span className="text-blue-400">scripts/dblp_crawler.py</span> 脚本生成的 output.txt 文件</li>
          <li>• <strong>更新:</strong> 重新运行爬虫脚本并上传新的 output.txt 文件即可更新数据</li>
          <li>• <strong>存储:</strong> 文件存储在服务器的 /data/dblp 目录中，可随时删除</li>
          <li>• <strong>数据格式:</strong> TXT 格式包含完整的论文信息（标题、作者、期刊/会议、年份、页码等）</li>
        </ul>
      </div>
    </div>
  );
};

export default DblpImportManager;
